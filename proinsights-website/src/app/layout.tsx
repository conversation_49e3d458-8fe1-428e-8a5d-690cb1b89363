import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "ProInsights Analytics - Transform Your Data Into Intelligence",
  description: "Empowering businesses with intelligent data analytics and insights. Transform your data into actionable intelligence with our cutting-edge platform.",
  keywords: "data analytics, business intelligence, data insights, analytics platform, data visualization",
  authors: [{ name: "ProInsights Analytics" }],
  openGraph: {
    title: "ProInsights Analytics - Transform Your Data Into Intelligence",
    description: "Empowering businesses with intelligent data analytics and insights.",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-white`}>
        <ThemeProvider>
          <div className="min-h-screen flex flex-col">
            <Navigation />
            <main className="flex-1 pt-16">
              {children}
            </main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
